# Add this service to your main docker-compose.yml file

  kafka-topic-init:
    image: confluentinc/cp-kafka:latest
    container_name: kafka-topic-init
    depends_on:
      kafka:
        condition: service_healthy
    volumes:
      - ./kafka-topic-init.sh:/kafka-topic-init.sh
    command: ["/bin/bash", "/kafka-topic-init.sh"]
    networks:
      - coupon-network
    restart: "no"  # Run once and exit

# Alternative approach using kafka-ui (with topic auto-creation)
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    depends_on:
      kafka:
        condition: service_healthy
    ports:
      - "8090:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
      # Auto-create topics on startup
      KAFKA_CLUSTERS_0_TOPICS_0_NAME: voucher-events
      KAFKA_CLUSTERS_0_TOPICS_0_PARTITIONS: 3
      KAFKA_CLUSTERS_0_TOPICS_1_NAME: order-events  
      KAFKA_CLUSTERS_0_TOPICS_1_PARTITIONS: 3
      KAFKA_CLUSTERS_0_TOPICS_2_NAME: user-events
      KAFKA_CLUSTERS_0_TOPICS_2_PARTITIONS: 3
      KAFKA_CLUSTERS_0_TOPICS_3_NAME: notification-events
      KAFKA_CLUSTERS_0_TOPICS_3_PARTITIONS: 3
    networks:
      - coupon-network
    restart: unless-stopped
