#!/bin/bash

# Wait for <PERSON><PERSON><PERSON> to be ready
echo "Waiting for <PERSON><PERSON><PERSON> to be ready..."
while ! kafka-topics --bootstrap-server kafka:29092 --list > /dev/null 2>&1; do
  echo "Kafka not ready yet, waiting..."
  sleep 2
done

echo "Kafka is ready! Creating topics..."

# Create topics if they don't exist
topics=(
  "voucher-events"
  "order-events" 
  "user-events"
  "notification-events"
)

for topic in "${topics[@]}"; do
  if kafka-topics --bootstrap-server kafka:29092 --list | grep -q "^${topic}$"; then
    echo "Topic '${topic}' already exists"
  else
    echo "Creating topic '${topic}'"
    kafka-topics --bootstrap-server kafka:29092 \
      --create \
      --topic "${topic}" \
      --partitions 3 \
      --replication-factor 1
    
    if [ $? -eq 0 ]; then
      echo "Successfully created topic '${topic}'"
    else
      echo "Failed to create topic '${topic}'"
    fi
  fi
done

echo "Topic initialization completed!"
