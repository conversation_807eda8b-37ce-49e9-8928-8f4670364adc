version: '3.8'

services:
  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka
    restart: unless-stopped
    environment:
      # KRaft mode configuration (no Zookeeper needed)
      KAFKA_NODE_ID: 1
      KAFKA_PROCESS_ROLES: broker,controller
      <PERSON><PERSON><PERSON>_CONTROLLER_QUORUM_VOTERS: 1@kafka:29093
      KAFKA_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:29092,PLAINTEXT_HOST://0.0.0.0:9092,CONTROLLER://0.0.0.0:29093
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT,CONTROLLER:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT

      # Cluster metadata configuration
      CLUSTER_ID: MkU3OEVBNTcwNTJENDM2Qk

      # Topic and replication settings
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'false'

      # Log settings
      KAFKA_LOG_RETENTION_HOURS: 168
      KAFKA_LOG_SEGMENT_BYTES: **********
      KAFKA_LOG_RETENTION_CHECK_INTERVAL_MS: 300000

      # KRaft specific settings
      KAFKA_LOG_DIRS: /var/lib/kafka/data
    ports:
      - "9092:9092"
      - "29092:29092"
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - coupon-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    restart: unless-stopped
    depends_on:
      kafka:
        condition: service_healthy
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    ports:
      - "8090:8080"
    networks:
      - coupon-network

  # Shared Jaeger (moved from individual services)
  jaeger:
    image: jaegertracing/all-in-one:1.56
    container_name: jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "6831:6831/udp"
    networks:
      - coupon-network

  # Shared Adminer for database management
  adminer:
    image: adminer:4.8.1
    container_name: adminer
    restart: unless-stopped
    ports:
      - "8091:8080"
    networks:
      - coupon-network

volumes:
  kafka-data:

networks:
  coupon-network:
    name: coupon-network
    driver: bridge
