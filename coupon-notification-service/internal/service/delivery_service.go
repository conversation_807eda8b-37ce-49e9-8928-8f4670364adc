package service

import (
	"context"

	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/model"
	"gitlab.zalopay.vn/phunn4/coupon-notification-service/internal/repository"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/config"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
)

type DeliveryService interface {
	DeliverNotification(ctx context.Context, notification *model.Notification) error
	RetryFailedDeliveries(ctx context.Context) error
}

type deliveryService struct {
	repo   repository.NotificationRepository
	logger *logging.Logger
	config *config.Config
}

func NewDeliveryService(repo repository.NotificationRepository, logger *logging.Logger, cfg *config.Config) DeliveryService {
	return &deliveryService{
		repo:   repo,
		logger: logger,
		config: cfg,
	}
}

func (ds *deliveryService) DeliverNotification(ctx context.Context, notification *model.Notification) error {
	log := ds.logger.WithContext(ctx).WithField("notification_id", notification.ID)

	// For in-app notifications, we just need to ensure they're stored in the database
	// which is already done when the notification is created
	log.Infof("In-app notification delivered for user %d", notification.UserID)
	return nil
}

func (ds *deliveryService) RetryFailedDeliveries(ctx context.Context) error {
	// Simplified implementation - no retry logic based on delivery logs
	// This method is kept for interface compatibility but does nothing
	ds.logger.WithContext(ctx).Info("Retry functionality has been simplified - no delivery tracking")
	return nil
}
